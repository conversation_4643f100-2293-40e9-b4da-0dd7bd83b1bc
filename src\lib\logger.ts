// lib/logger.ts
import { nanoid } from "nanoid";
import pino from "pino";

const isDevelopment = process.env.NODE_ENV === "development";

export const logger = pino({
  level: process.env.LOG_LEVEL || "info",
  transport: isDevelopment
    ? {
        target: "pino-pretty",
        options: {
          colorize: true,
          translateTime: "SYS:standard",
          ignore: "pid,hostname",
        },
      }
    : undefined,
  formatters: {
    level: (label) => {
      return { level: label.toUpperCase() };
    },
  },
  hooks: {
    logMethod(inputArgs, method) {
      const correlationId: any = (global as any).correlationId || nanoid();
      const args = [...inputArgs];

      if (typeof args[0] === "object") {
        args[0] = {
          correlationId,
          ...args[0],
        };
      } else {
        args.unshift({ correlationId });
      }

      method.apply(this, args as any);
    },
  },
});

// Middleware untuk menambahkan correlation ID
export function loggingMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
) {
  const correlationId = nanoid();
  (global as any).correlationId = correlationId;

  const startTime = Date.now();

  logger.info({
    type: "http-request",
    method: req.method,
    url: req.url,
    correlationId,
    userAgent: req.headers["user-agent"],
  });

  res.on("finish", () => {
    const duration = Date.now() - startTime;
    logger.info({
      type: "http-response",
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      correlationId,
    });

    // Cleanup
    delete (global as any).correlationId;
  });

  next();
}
